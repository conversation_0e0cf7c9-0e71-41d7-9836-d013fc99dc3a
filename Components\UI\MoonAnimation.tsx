import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

interface MoonAnimationProps {
  isNightMode: boolean; // true quand mode = 'night'
  currentMode: string;  // pour détecter les changements de mode
}

const MoonAnimation: React.FC<MoonAnimationProps> = ({ isNightMode, currentMode }) => {
  const moonRef = useRef<HTMLDivElement>(null);
  const haloRef = useRef<HTMLDivElement>(null); // 🔧 CISCO: Référence séparée pour le halo
  const animationRef = useRef<gsap.core.Timeline | null>(null);
  const fadeOutRef = useRef<gsap.core.Tween | null>(null);

  useEffect(() => {
    if (!moonRef.current || !haloRef.current) return;

    // 🌙 CISCO: Mode Nuit profonde - Apparition et descente de la lune
    if (isNightMode && currentMode === 'night') {
      console.log('🌙 Mode Nuit profonde activé - Animation de la lune');

      // Arrêter toute animation en cours
      if (animationRef.current) {
        animationRef.current.kill();
      }
      if (fadeOutRef.current) {
        fadeOutRef.current.kill();
      }

      // Position initiale : juste sous le header, très à gauche, invisible
      gsap.set(moonRef.current, {
        x: '10vw', // 🔧 CISCO: Encore plus à gauche, limite du menu header
        y: '40px', // 🔧 CISCO: Encore plus haut, juste sous le header
        xPercent: -50,
        opacity: 0,
        scale: 1,
        display: 'block'
      });

      // Position initiale du halo (même position que la lune)
      gsap.set(haloRef.current, {
        x: '10vw',
        y: '40px', // 🔧 CISCO: Même position que la lune
        xPercent: -50,
        opacity: 0,
        display: 'block'
      });

      // Créer la timeline d'animation
      animationRef.current = gsap.timeline();

      // Phase 1: Apparition douce de la lune et du halo (3 secondes)
      animationRef.current.to(moonRef.current, {
        opacity: 0.9, // 🔧 CISCO: Légèrement plus opaque
        duration: 3,
        ease: "power2.out"
      });

      // Apparition du halo en parallèle
      animationRef.current.to(haloRef.current, {
        opacity: 0.15, // 🔧 CISCO: Halo très subtil
        duration: 3,
        ease: "power2.out"
      }, 0); // En même temps que la lune

      // Phase 2: Descente diagonale ultra-lente vers l'arbre (15 minutes) - 🔧 CISCO: Encore plus lente
      animationRef.current.to(moonRef.current, {
        y: '600px', // 🔧 CISCO: Descente vers le bas de l'écran
        x: '90vw', // 🔧 CISCO: Mouvement diagonal vers l'extrême droite (opposé de l'écran)
        duration: 900, // 15 minutes - ultra-ultra-lent
        ease: "power1.inOut" // Mouvement légèrement accéléré au milieu, plus naturel
      }, "-=1"); // Commence 1 seconde avant la fin de l'apparition

      // Mouvement du halo synchronisé avec la lune
      animationRef.current.to(haloRef.current, {
        y: '600px', // 🔧 CISCO: Descente vers le bas de l'écran
        x: '90vw', // 🔧 CISCO: Vers l'extrême droite
        duration: 900, // Même durée que la lune
        ease: "power1.inOut"
      }, "-=901"); // Synchronisé avec la lune

      console.log('🌙 Animation de descente diagonale ultra-lente lancée (900 secondes - 15 minutes) vers l\'arbre');

    } else if (!isNightMode || currentMode !== 'night') {
      // 🌅 CISCO: Autre mode - Disparition de la lune (OBLIGATOIRE pour tous les modes sauf 'night')
      console.log(`🌅 Mode ${currentMode} activé - Disparition OBLIGATOIRE de la lune`);

      // Arrêter l'animation de descente
      if (animationRef.current) {
        animationRef.current.kill();
        animationRef.current = null;
      }

      // Si la lune est visible, la faire disparaître en douceur avec le halo
      if (moonRef.current && gsap.getProperty(moonRef.current, "opacity") > 0) {
        fadeOutRef.current = gsap.timeline();

        // Disparition de la lune
        fadeOutRef.current.to(moonRef.current, {
          opacity: 0,
          duration: 4, // Disparition sur 4 secondes
          ease: "power2.in"
        });

        // Disparition du halo en parallèle
        fadeOutRef.current.to(haloRef.current, {
          opacity: 0,
          duration: 4,
          ease: "power2.in",
          onComplete: () => {
            if (moonRef.current && haloRef.current) {
              gsap.set(moonRef.current, { display: 'none' });
              gsap.set(haloRef.current, { display: 'none' });
            }
            console.log('🌙 Lune et halo disparus');
          }
        }, 0); // En même temps que la lune
      } else {
        // Si déjà invisible, juste les cacher
        gsap.set(moonRef.current, { display: 'none' });
        gsap.set(haloRef.current, { display: 'none' });
      }
    }

    // Nettoyage au démontage
    return () => {
      if (animationRef.current) {
        animationRef.current.kill();
      }
      if (fadeOutRef.current) {
        fadeOutRef.current.kill();
      }
    };
  }, [isNightMode, currentMode]);

  return (
    <>
      {/* 🌙 CISCO: Halo lumineux séparé pour éviter l'effet carré */}
      <div
        ref={haloRef}
        className="fixed top-0 left-0 pointer-events-none"
        style={{
          zIndex: 8, // 🔧 CISCO: Z-index 8 pour être devant les étoiles (1) mais derrière les nuages (10-12)
          display: 'none',
          width: '400px', // Halo plus large que la lune
          height: '400px',
          background: 'radial-gradient(circle, rgba(255, 255, 255, 0.06) 0%, rgba(255, 255, 255, 0.03) 30%, rgba(255, 255, 255, 0.01) 60%, transparent 100%)', // 🔧 CISCO: Halo encore plus subtil
          borderRadius: '50%',
          transform: 'translate(-50%, -50%)', // Centré sur la position
        }}
      />

      {/* 🌙 CISCO: Lune principale sans halo pour éviter l'effet carré */}
      <div
        ref={moonRef}
        className="fixed top-0 left-0 pointer-events-none"
        style={{
          zIndex: 8, // 🔧 CISCO: Z-index 8 pour être devant les étoiles (1) mais derrière les nuages (10-12)
          display: 'none', // Initialement cachée
          width: '180px', // 🔧 CISCO: Plus grosse (120px -> 180px)
          height: '180px', // 🔧 CISCO: Plus grosse (120px -> 180px)
          backgroundImage: 'url(/Lune-Moon.png)',
          backgroundSize: 'contain',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          // 🔧 CISCO: Suppression des filtres pour éviter l'effet carré
        }}
        title="🌙 Lune nocturne"
      />
    </>
  );
};

export default MoonAnimation;
